import httpx

from collections.abc import Generator
from fastapi import Depends, HTTPException, Request
from sqlmodel import Session
from typing import Annotated

from app.core.config import settings
from app.core.db import engine
from app.utils.logging import logger

from portal_common.models.response_body import ResponseBody
from portal_common.models.user import User
from portal_perm.models import Perm
from portal_perm.utils import configure_access


def get_db() -> Generator[Session, None, None]:
    with Session(engine) as session:
        yield session


Database = Annotated[Session, Depends(get_db)]


async def get_current_user(request: Request) -> User:
    async with httpx.AsyncClient() as client:
        headers = {**request.headers}
        secret = settings.PORTAL_SERVICE_CONSUMER_SECRET
        if secret:
            headers["Authorization"] = f"Bearer {secret}"

        response = await client.get(
            f"{settings.PORTAL_ACCOUNT_SERVICE_PREFIX}/api/v1/account/info",
            headers=headers,
        )
        try:
            data = response.raise_for_status().json()
            return ResponseBody[User].model_validate(data).item
        except Exception as e:
            logger.error(f"Failed to get user info: {e}")

            raise HTTPException(status_code=403, detail="Invalid session provided")


CurrentUser = Annotated[User, Depends(get_current_user)]


def get_user_perms(user: CurrentUser) -> list[Perm]:
    perms: list[Perm] = []

    for p in user.permissions:
        try:
            perms.append(Perm.parse(p))
        except ValueError:
            logger.warning(f"{p} is not a valid permission!")

    return perms


Perms = Annotated[list[Perm], Depends(get_user_perms)]

access = configure_access(get_perms=get_user_perms)
