from fastapi import APIRouter

from app.models.items import ItemResponse
from app.api.deps import Database, Perms, access
from app.services.items import get_items
from app.utils.logging import logger


from portal_common.models.response_body import ListResponseBody
from portal_perm.constants import perms
from portal_perm.utils import filter_field_by_perms


router = APIRouter(tags=["items"], prefix="/items")


@router.get(
    "",
    dependencies=[access(perms.api_portal_example_items_read)],
    response_model=ListResponseBody[ItemResponse],
)
def get_list(db: Database, perms: Perms):
    logger.info(perms)
    items = get_items(db=db)
    items = [ItemResponse.model_validate(item) for item in items]
    items = filter_field_by_perms(perms=perms, data=items)

    return ListResponseBody(items=items, count=len(items))


@router.get(
    "",
    dependencies=[access(perms.api_portal_example_items_read)],
    response_model=ListResponseBody[ItemResponse],
)
def get_list(db: Database, perms: Perms):
    logger.info(perms)
    items = get_items(db=db)
    items = [ItemResponse.model_validate(item) for item in items]
    items = filter_field_by_perms(perms=perms, data=items)

    return ListResponseBody(items=items, count=len(items))
