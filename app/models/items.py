from sqlmodel import Field, SQLModel
from typing import Dict

from portal_perm.constants import perms
from portal_perm.models import Perm


class ItemBase(SQLModel):
    name: str
    code: str


class Item(ItemBase, table=True):
    id: int | None = Field(default=None, primary_key=True)


class ItemResponse(ItemBase):
    id: int

    def __acls__(self) -> Dict[str, Perm]:
        return {"code": perms.field_portal_example_items_code_read}
