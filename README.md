数据库 migration
app/alembic/env.py

重点 1：
alembic 在 migration 时会删除 app/models 中没有定义的表，但是我们只希望它不要乱删表，所以定义 include_object 参数来实现这一点

重点 2:
每个微服务都需要用到 migration，而 alembic 会自动在 alembic_version 表中记录 migration 的版本号，所以每个微服务的 alembic_version 表中记录的版本号是不同的，为了解决这个问题，我们定义了 version_table 参数，将每个微服务的 alembic_version 表名改为不同的名字

权限系统接入
app/api/deps.py

get_current_user 函数:
展示了如何获取当前用户信息。具体方式为向 portal_account 发送请求，获取当前用户信息。需要把依赖注入的 request 中的 headers 传递给 portal_account，同时添加上 Authorization 头，值为 Bearer + settings.PORTAL_SERVICE_CONSUMER_SECRET，这个是用来做网关的服务间调用认证，本地开发时可以留空，但是逻辑一定要有，不然生产环境请求会被拒

access 函数:
用来限制 api 接口的权限。调用 portal_perm.utils.configure_access 获取，需要传入获取用户权限列表的参数 get_perms。可以参考这里的 get_user_perms 函数.



权限系统使用
app/routes/items.py

api 接口权限：
在路由上配置 dependencies=[access(perms.api_portal_example_items_read)]，则用户需要有 perms.api_portal_example_items_read 权限才能访问这个接口

字段权限：
需要在路由上设置 response_model，model 需要为 pandatic.BaseModel。model 需要定义 __acls__ 方法，返回的结构为 field 与 权限的映射，如 {"code": perms.field_portal_example_items_code_read}，则用户需要有 perms.field_portal_example_items_code_read 权限才能看到 code 字段.接口在返回前需要调用 portal_perm.filter_field_by_perms 函数，传入当前用户的权限列表以及业务数据。(用户权限列表可以通过上面提到的 get_user_perms 函数来获取)(业务数据一定要是 pandatic.BaseModel)

条目权限：
检查当前用户的权限列表中是否包含需要的权限，然后自定义过滤逻辑。如：

items = get_items(db=db)
if "record:portal_example:items.event:read" not in perms:
    items = [item for item in items if item.code != "event"]

实际使用时一般会在判断用户无权限后添加数据库过滤逻辑。
